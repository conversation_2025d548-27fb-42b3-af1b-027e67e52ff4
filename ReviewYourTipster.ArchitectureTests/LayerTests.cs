using FluentAssertions;
using NetArchTest.Rules;

namespace ReviewYourTipster.ArchitectureTests;

public class LayerTests
{
    [Fact]
    public void Domain_Should_NotHaveDependencyOnApplication()
    {
        TestResult result = Types.InAssembly(Layers.DomainAssembly).Should()
            .NotHaveDependencyOn("ReviewYourTipster.Application").GetResult();

        result.IsSuccessful.Should().BeTrue();
    }

    [Fact]
    public void Domain_Should_NotHaveDependencyOnInfrastructure()
    {
        TestResult result = Types.InAssembly(Layers.DomainAssembly).Should()
            .NotHaveDependencyOn("ReviewYourTipster.Infrastructure").GetResult();

        result.IsSuccessful.Should().BeTrue();
    }

    [Fact]
    public void Domain_Should_NotHaveDependencyOnPresentation()
    {
        TestResult result = Types.InAssembly(Layers.DomainAssembly).Should()
            .NotHaveDependencyOn("ReviewYourTipster.WebAPI").GetResult();

        result.IsSuccessful.Should().BeTrue();
    }

    [Fact]
    public void Application_Should_NotHaveDependencyOnInfrastructure()
    {
        TestResult result = Types.InAssembly(Layers.ApplicationAssembly).Should()
            .NotHaveDependencyOn("ReviewYourTipster.Infrastructure").GetResult();

        result.IsSuccessful.Should().BeTrue();
    }

    [Fact]
    public void Application_Should_NotHaveDependencyOnPresentation()
    {
        TestResult result = Types.InAssembly(Layers.ApplicationAssembly).Should()
            .NotHaveDependencyOn("ReviewYourTipster.WebAPI").GetResult();

        result.IsSuccessful.Should().BeTrue();
    }

    [Fact]
    public void Infrastructure_Should_NotHaveDependencyOnPresentation()
    {
        TestResult result = Types.InAssembly(Layers.InfrastructureAssembly).Should()
            .NotHaveDependencyOn("ReviewYourTipster.WebAPI").GetResult();

        result.IsSuccessful.Should().BeTrue();
    }
}