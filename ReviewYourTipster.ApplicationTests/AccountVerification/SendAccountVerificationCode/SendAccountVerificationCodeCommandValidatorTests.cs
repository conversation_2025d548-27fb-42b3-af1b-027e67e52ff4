using FluentValidation.TestHelper;
using ReviewYourTipster.Application.AccountVerification.SendAccountVerificationCode;

namespace ReviewYourTipster.ApplicationTests.AccountVerification.SendAccountVerificationCode;

public class SendAccountVerificationCodeCommandValidatorTests
{
    private readonly SendAccountVerificationCodeCommandValidator _validator = new();

    [Fact]
    public void Validate_ReturnsRequiredError_WhenAddressIsEmpty()
    {
        // Arrange
        var command = new SendAccountVerificationCodeCommand { Address = string.Empty };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Address)
            .WithErrorMessage("Address is required")
            .WithErrorCode("Address.Required");
    }

    [Fact]
    public void Validate_ReturnsRequiredError_WhenAddressIsNull()
    {
        // Arrange
        var command = new SendAccountVerificationCodeCommand { Address = null! };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Address)
            .WithErrorCode("Address.Required");
    }

    [Fact]
    public void Validate_ReturnsRequiredError_WhenAddressIsWhitespace()
    {
        // Arrange
        var command = new SendAccountVerificationCodeCommand { Address = "   " };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Address)
            .WithErrorMessage("Address is required")
            .WithErrorCode("Address.Required");
    }

    [Theory]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("<EMAIL>")]
    [InlineData("user@domain.")]
    public void Validate_PassesValidation_WhenAddressIsValidEmail(string email)
    {
        // Arrange
        var command = new SendAccountVerificationCodeCommand { Address = email };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Address);
    }

    [Theory]
    [InlineData("+*************")] // São Paulo mobile
    [InlineData("+*************")] // Rio de Janeiro mobile
    [InlineData("+*************")] // São Paulo landline
    [InlineData("+*************")] // Rio de Janeiro landline
    public void Validate_PassesValidation_WhenAddressIsValidBrazilianPhone(string phone)
    {
        // Arrange
        var command = new SendAccountVerificationCodeCommand { Address = phone };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Address);
    }

    [Theory]
    [InlineData("invalid-email")]
    [InlineData("@domain.com")]
    [InlineData("user@")]
    [InlineData("user@domain")]
    [InlineData("user@@domain.com")]
    public void Validate_ReturnsInvalidFormatError_WhenAddressIsInvalidEmail(string invalidEmail)
    {
        // Arrange
        var command = new SendAccountVerificationCodeCommand { Address = invalidEmail };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Address)
            .WithErrorMessage("Address must be a valid email or phone number")
            .WithErrorCode("Address.InvalidFormat");
    }

    [Theory]
    [InlineData("***********")] // Missing country code
    [InlineData("+***********")] // Too short
    [InlineData("+*************23")] // Too long
    [InlineData("+*************")] // Wrong country code
    [InlineData("*************")] // Missing +
    [InlineData("+55abc87654321")] // Contains letters
    public void Validate_ReturnsInvalidFormatError_WhenAddressIsInvalidBrazilianPhone(string invalidPhone)
    {
        // Arrange
        var command = new SendAccountVerificationCodeCommand { Address = invalidPhone };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Address)
            .WithErrorMessage("Address must be a valid email or phone number")
            .WithErrorCode("Address.InvalidFormat");
    }

    [Fact]
    public void Validate_ReturnsInvalidFormatError_WhenAddressIsNeitherEmailNorPhone()
    {
        // Arrange
        var command = new SendAccountVerificationCodeCommand { Address = "just-some-text" };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Address)
            .WithErrorMessage("Address must be a valid email or phone number")
            .WithErrorCode("Address.InvalidFormat");
    }
}