using ReviewYourTipster.Application.Abstractions.Authentication;
using ReviewYourTipster.Application.Abstractions.Common;
using ReviewYourTipster.Application.Abstractions.Data.Repositories;
using ReviewYourTipster.Application.Abstractions.Messaging;
using ReviewYourTipster.Domain.Users;
using ReviewYourTipster.SharedKernel;

namespace ReviewYourTipster.Application.AccountVerification.SendAccountVerificationCode;

public class
    SendAccountVerificationCodeCommandHandler(
        IUserRepository userRepository,
        ICodeProvider codeProvider,
        IUserContext userContext,
        IVerificationMessageService verificationMessageService)
    : ICommandHandler<SendAccountVerificationCodeCommand, bool>
{
    private const int VerificationCodeLifetimeMinutes = 10;

    public async Task<Result<bool>> Handle(SendAccountVerificationCodeCommand request,
        CancellationToken cancellationToken)
    {
        // Get the currently logged-in user
        var loggedInUserId = userContext.UserId;
        var user = await userRepository.GetUserByIdAsync(loggedInUserId, cancellationToken);

        // Validate if user exists
        if (user is null)
            return Result.Failure<bool>(UserErrors.UserNotFound);

        // Check if user is already verified
        if (user.IsVerified)
            return Result.Failure<bool>(UserErrors.UserAlreadyVerified);

        // Generate verification code
        var code = codeProvider.GenerateCode(
            user.Id.ToString(),
            VerificationCodePurposes.AccountVerification,
            TimeSpan.FromMinutes(VerificationCodeLifetimeMinutes));

        // Determine verification channel based on address format
        var channel = DetermineVerificationChannel(request.Address);

        // Send verification code
        verificationMessageService.SendVerificationCode(channel, request.Address, code);

        return Result.Success(true);
    }

    /// <summary>
    ///     Determines the verification channel based on the address format
    /// </summary>
    private static VerificationMessageChannel DetermineVerificationChannel(string address)
    {
        return address.Contains('@') ? VerificationMessageChannel.Email : VerificationMessageChannel.Sms;
    }
}