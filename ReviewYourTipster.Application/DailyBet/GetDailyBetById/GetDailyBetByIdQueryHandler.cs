using ReviewYourTipster.Application.Abstractions.Data.Repositories;
using ReviewYourTipster.Application.Abstractions.Messaging;
using ReviewYourTipster.Application.DataTransferObjects.DailyBet;
using ReviewYourTipster.Domain.DailyBet;
using ReviewYourTipster.SharedKernel;

namespace ReviewYourTipster.Application.DailyBet.GetDailyBetById;

public class GetDailyBetByIdQueryHandler(IDailyBetRepository dailyBetRepository)
    : IQueryHandler<GetDailyBetByIdQuery, DailyBetDto>
{
    public async Task<Result<DailyBetDto>> Handle(GetDailyBetByIdQuery request, CancellationToken cancellationToken)
    {
        var dailyBet = await dailyBetRepository.GetByIdAsync(request.DailyBetId, cancellationToken);

        if (dailyBet is null)
            return Result.Failure<DailyBetDto>(DailyBetErrors.DailyBetNotFound);

        return dailyBet.ToDto();
    }
}