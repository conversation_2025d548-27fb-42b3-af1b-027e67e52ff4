using ReviewYourTipster.Application.Abstractions.Data.Repositories;
using ReviewYourTipster.Application.Abstractions.Messaging;
using ReviewYourTipster.Application.DataTransferObjects.DailyBet;
using ReviewYourTipster.SharedKernel;

namespace ReviewYourTipster.Application.DailyBet.GetActiveBetOfTheDay;

public class GetActiveBetOfTheDayQueryHandler(IDailyBetRepository dailyBetRepository)
    : IQueryHandler<GetActiveBetOfTheDayQuery, DailyBetDto?>
{
    public async Task<Result<DailyBetDto?>> Handle(GetActiveBetOfTheDayQuery request,
        CancellationToken cancellationToken)
    {
        var activeBetOfTheDay = await dailyBetRepository.GetActiveBetOfTheDayAsync(cancellationToken);

        return Result.Success(activeBetOfTheDay?.ToDto());
    }
}