using FluentValidation;
using ReviewYourTipster.SharedKernel.Common;

namespace ReviewYourTipster.Application.AccountVerification.SendAccountVerificationCode;

/// <summary>
///     Validator for the send account verification code command
/// </summary>
public class SendAccountVerificationCodeCommandValidator : AbstractValidator<SendAccountVerificationCodeCommand>
{
    public SendAccountVerificationCodeCommandValidator()
    {
        RuleFor(x => x.Address)
            .NotEmpty().WithMessage("Address is required")
            .WithErrorCode("Address.Required")
            .Must(address => IsValidEmailOrPhone(address))
            .WithMessage("Address must be a valid email or phone number")
            .WithErrorCode("Address.InvalidFormat");
    }

    private static bool IsValidEmailOrPhone(string address)
    {
        if (string.IsNullOrWhiteSpace(address))
            return false;

        if (address.Contains('@'))
        {
            var emailParts = address.Split('@');
            return emailParts.Length == 2 &&
                   !string.IsNullOrWhiteSpace(emailParts[0]) &&
                   !string.IsNullOrWhiteSpace(emailParts[1]) &&
                   emailParts[1].Contains('.');
        }

        // If not an email, check if it's a valid Brazilian phone number
        return Validations.BeAValidBrazilianPhone(address);
    }
}