using FluentAssertions;
using Moq;
using ReviewYourTipster.Application.Abstractions.Authentication;
using ReviewYourTipster.Application.Abstractions.Common;
using ReviewYourTipster.Application.Abstractions.Data.Repositories;
using ReviewYourTipster.Application.AccountVerification.SendAccountVerificationCode;
using ReviewYourTipster.Domain.Users;
using ReviewYourTipster.SharedKernel;

namespace ReviewYourTipster.ApplicationTests.AccountVerification.SendAccountVerificationCode;

public class SendAccountVerificationCodeCommandHandlerTests
{
    private readonly Mock<IUserRepository> _userRepositoryMock;
    private readonly Mock<ICodeProvider> _codeProviderMock;
    private readonly Mock<IUserContext> _userContextMock;
    private readonly Mock<IVerificationMessageService> _verificationMessageServiceMock;
    private readonly SendAccountVerificationCodeCommandHandler _handler;

    public SendAccountVerificationCodeCommandHandlerTests()
    {
        _userRepositoryMock = new Mock<IUserRepository>();
        _codeProviderMock = new Mock<ICodeProvider>();
        _userContextMock = new Mock<IUserContext>();
        _verificationMessageServiceMock = new Mock<IVerificationMessageService>();

        _handler = new SendAccountVerificationCodeCommandHandler(
            _userRepositoryMock.Object,
            _codeProviderMock.Object,
            _userContextMock.Object,
            _verificationMessageServiceMock.Object);
    }

    [Fact]
    public async Task Handle_ReturnsSuccess_WhenUserExistsAndNotVerifiedWithEmailAddress()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var emailAddress = "<EMAIL>";
        var verificationCode = "123456";

        var user = new User
        {
            Id = userId,
            Name = "Test User",
            Email = "<EMAIL>",
            IsVerified = false
        };

        var command = new SendAccountVerificationCodeCommand { Address = emailAddress };

        _userContextMock.Setup(x => x.UserId).Returns(userId);
        _userRepositoryMock.Setup(x => x.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);
        _codeProviderMock.Setup(x => x.GenerateCode(
                userId.ToString(),
                VerificationCodePurposes.AccountVerification,
                TimeSpan.FromMinutes(10)))
            .Returns(verificationCode);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeTrue();

        _verificationMessageServiceMock.Verify(x => x.SendVerificationCode(
            VerificationMessageChannel.Email,
            emailAddress,
            verificationCode), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnsSuccess_WhenUserExistsAndNotVerifiedWithPhoneNumber()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var phoneNumber = "+*************";
        var verificationCode = "654321";

        var user = new User
        {
            Id = userId,
            Name = "Test User",
            Email = "<EMAIL>",
            IsVerified = false
        };

        var command = new SendAccountVerificationCodeCommand { Address = phoneNumber };

        _userContextMock.Setup(x => x.UserId).Returns(userId);
        _userRepositoryMock.Setup(x => x.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);
        _codeProviderMock.Setup(x => x.GenerateCode(
                userId.ToString(),
                VerificationCodePurposes.AccountVerification,
                TimeSpan.FromMinutes(10)))
            .Returns(verificationCode);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().BeTrue();

        _verificationMessageServiceMock.Verify(x => x.SendVerificationCode(
            VerificationMessageChannel.Sms,
            phoneNumber,
            verificationCode), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnsUserNotFoundError_WhenUserDoesNotExist()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var command = new SendAccountVerificationCodeCommand { Address = "<EMAIL>" };

        _userContextMock.Setup(x => x.UserId).Returns(userId);
        _userRepositoryMock.Setup(x => x.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(UserErrors.UserNotFound);

        _codeProviderMock.Verify(x => x.GenerateCode(
            It.IsAny<string>(),
            It.IsAny<VerificationCodePurposes>(),
            It.IsAny<TimeSpan>()), Times.Never);

        _verificationMessageServiceMock.Verify(x => x.SendVerificationCode(
            It.IsAny<VerificationMessageChannel>(),
            It.IsAny<string>(),
            It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ReturnsUserAlreadyVerifiedError_WhenUserIsAlreadyVerified()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var command = new SendAccountVerificationCodeCommand { Address = "<EMAIL>" };

        var user = new User
        {
            Id = userId,
            Name = "Test User",
            Email = "<EMAIL>",
            IsVerified = true // User is already verified
        };

        _userContextMock.Setup(x => x.UserId).Returns(userId);
        _userRepositoryMock.Setup(x => x.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Should().Be(UserErrors.UserAlreadyVerified);

        _codeProviderMock.Verify(x => x.GenerateCode(
            It.IsAny<string>(),
            It.IsAny<VerificationCodePurposes>(),
            It.IsAny<TimeSpan>()), Times.Never);

        _verificationMessageServiceMock.Verify(x => x.SendVerificationCode(
            It.IsAny<VerificationMessageChannel>(),
            It.IsAny<string>(),
            It.IsAny<string>()), Times.Never);
    }

    [Theory]
    [InlineData("<EMAIL>", VerificationMessageChannel.Email)]
    [InlineData("<EMAIL>", VerificationMessageChannel.Email)]
    [InlineData("<EMAIL>", VerificationMessageChannel.Email)]
    [InlineData("+*************", VerificationMessageChannel.Sms)]
    [InlineData("+5521987654321", VerificationMessageChannel.Sms)]
    [InlineData("+*************", VerificationMessageChannel.Sms)]
    public async Task Handle_UsesCorrectVerificationChannel_BasedOnAddressFormat(
        string address,
        VerificationMessageChannel expectedChannel)
    {
        // Arrange
        var userId = Guid.NewGuid();
        var verificationCode = "123456";

        var user = new User
        {
            Id = userId,
            Name = "Test User",
            Email = "<EMAIL>",
            IsVerified = false
        };

        var command = new SendAccountVerificationCodeCommand { Address = address };

        _userContextMock.Setup(x => x.UserId).Returns(userId);
        _userRepositoryMock.Setup(x => x.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);
        _codeProviderMock.Setup(x => x.GenerateCode(
                userId.ToString(),
                VerificationCodePurposes.AccountVerification,
                TimeSpan.FromMinutes(10)))
            .Returns(verificationCode);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        _verificationMessageServiceMock.Verify(x => x.SendVerificationCode(
            expectedChannel,
            address,
            verificationCode), Times.Once);
    }

    [Fact]
    public async Task Handle_GeneratesCodeWithCorrectParameters_WhenUserIsValid()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var emailAddress = "<EMAIL>";
        var expectedLifetime = TimeSpan.FromMinutes(10);

        var user = new User
        {
            Id = userId,
            Name = "Test User",
            Email = "<EMAIL>",
            IsVerified = false
        };

        var command = new SendAccountVerificationCodeCommand { Address = emailAddress };

        _userContextMock.Setup(x => x.UserId).Returns(userId);
        _userRepositoryMock.Setup(x => x.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(user);
        _codeProviderMock.Setup(x => x.GenerateCode(
                It.IsAny<string>(),
                It.IsAny<VerificationCodePurposes>(),
                It.IsAny<TimeSpan>()))
            .Returns("123456");

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        _codeProviderMock.Verify(x => x.GenerateCode(
            userId.ToString(),
            VerificationCodePurposes.AccountVerification,
            expectedLifetime), Times.Once);
    }

    [Fact]
    public async Task Handle_CallsRepositoryWithCorrectUserId_WhenHandlingRequest()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var command = new SendAccountVerificationCodeCommand { Address = "<EMAIL>" };

        _userContextMock.Setup(x => x.UserId).Returns(userId);
        _userRepositoryMock.Setup(x => x.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((User?)null);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _userRepositoryMock.Verify(x => x.GetUserByIdAsync(userId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_PassesCancellationToken_ToRepository()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var command = new SendAccountVerificationCodeCommand { Address = "<EMAIL>" };
        var cancellationToken = new CancellationToken();

        _userContextMock.Setup(x => x.UserId).Returns(userId);
        _userRepositoryMock.Setup(x => x.GetUserByIdAsync(userId, cancellationToken))
            .ReturnsAsync((User?)null);

        // Act
        await _handler.Handle(command, cancellationToken);

        // Assert
        _userRepositoryMock.Verify(x => x.GetUserByIdAsync(userId, cancellationToken), Times.Once);
    }
}