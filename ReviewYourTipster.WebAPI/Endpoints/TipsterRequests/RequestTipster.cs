using MediatR;
using Microsoft.AspNetCore.Mvc;
using ReviewYourTipster.Application.Tipster.Request;
using ReviewYourTipster.Domain.TipsterRequests;
using ReviewYourTipster.WebAPI.Extensions;
using ReviewYourTipster.WebAPI.Infrastructure;

namespace ReviewYourTipster.WebAPI.Endpoints.TipsterRequests;

public class RequestTipster : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/tipster/requests", async (RequestTipsterRequest requestTipsterRequest, ISender sender,
                CancellationToken cancellationToken) =>
            {
                var command = new RequestTipsterCommand
                {
                    Origin = requestTipsterRequest.Origin,
                    Name = requestTipsterRequest.Name,
                    Instagram = requestTipsterRequest.Instagram,
                    Telegram = requestTipsterRequest.Telegram,
                    Email = requestTipsterRequest.Email,
                    Phone = requestTipsterRequest.Phone,
                    Descriptions = requestTipsterRequest.Descriptions,
                    Recommendation = requestTipsterRequest.Recommendation
                };

                var result = await sender.Send(command, cancellationToken);

                return result.Match(Results.Ok, CustomResults.Problem);
            }).RequireAuthorization().WithTags(Tags.Tipster)
            .WithSummary("Requests a tipster")
            .WithDescription("Requests a tipster")
            .Produces<ProblemDetails>(StatusCodes.Status400BadRequest);
    }

    private sealed class RequestTipsterRequest
    {
        public required TipsterRequestOrigin Origin { get; init; }
        public required string Name { get; init; }
        public required string Instagram { get; init; }
        public required string Telegram { get; init; }
        public string? Email { get; init; }
        public string? Phone { get; init; }
        public List<string>? Descriptions { get; init; }
        public string? Recommendation { get; init; }
    }
}